namespace PasswordHistoryValidator.Shared;

/// <summary>
/// SendGrid email service configuration options
/// </summary>
public class SendGridOptions
{
    public const string SectionName = "SendGrid";
    
    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
}

/// <summary>
/// Entra External ID configuration options
/// </summary>
public class EntraOptions
{
    public const string SectionName = "EntraExternalID";
    
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string DefaultDomain { get; set; } = "yourtenant.onmicrosoft.com";
}

/// <summary>
/// Password reset configuration options
/// </summary>
public class PasswordResetOptions
{
    public const string SectionName = "PasswordReset";
    
    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Rate limiting configuration options
/// </summary>
public class RateLimitOptions
{
    public const string SectionName = "RateLimit";
    
    public int MaxRequestsPerMinute { get; set; } = 60;
}

/// <summary>
/// Azure Storage configuration options
/// </summary>
public class StorageOptions
{
    public const string SectionName = "Storage";
    
    public string ConnectionString { get; set; } = string.Empty;
}
