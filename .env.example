# Environment Variables Configuration
# Copy this file to .env and fill in your actual values
# Note: The application now uses strongly-typed configuration sections in local.settings.json
# These environment variables serve as fallbacks

# SendGrid Configuration (Essential Settings Only)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# Password Reset Configuration
RESET_PASSWORD_BASE_URL=https://auth.tylerlovell.com/reset-password

# Azure Function Configuration
AzureWebJobsStorage=UseDevelopmentStorage=true
FUNCTIONS_WORKER_RUNTIME=dotnet-isolated

# Entra External ID Configuration
EntraExternalID:TenantId=your_tenant_id_here
EntraExternalID:ClientId=your_client_id_here
EntraExternalID:ClientSecret=your_client_secret_here
EntraExternalID:DefaultDomain=yourtenant.onmicrosoft.com

# Storage Configuration
StorageConnectionString=UseDevelopmentStorage=true

# Application Configuration
ApplicationName=Password Reset Service

# Password History Configuration
PasswordHistory:MaxCount=12
PasswordHistory:WorkFactor=12

# Rate Limiting
RateLimit:MaxRequestsPerMinute=60

# Graph API
GraphApiEndpoint=https://graph.microsoft.com/v1.0
