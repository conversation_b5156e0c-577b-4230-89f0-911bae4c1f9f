using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Base class for Azure Functions providing common response handling and CORS functionality
/// </summary>
public abstract class BaseFunctionService
{
    protected readonly JsonSerializerOptions JsonOptions;

    protected BaseFunctionService(JsonSerializerOptions jsonOptions)
    {
        JsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
    }

    #region Response Methods

    /// <summary>
    /// Creates a JSON response with the specified data and status code
    /// </summary>
    protected async Task<HttpResponseData> CreateJsonResponse<T>(HttpRequestData req, T data, HttpStatusCode statusCode, string correlationId)
    {
        var response = req.CreateResponse(statusCode);
        var result = new
        {
            data = data,
            correlationId = correlationId,
            timestamp = DateTime.UtcNow
        };

        response.Headers.Add("Content-Type", "application/json");
        var json = JsonSerializer.Serialize(result, JsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    /// <summary>
    /// Creates an error response with the specified message and status code
    /// </summary>
    protected async Task<HttpResponseData> CreateErrorResponse(HttpRequestData req, string message, string correlationId, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
    {
        var response = req.CreateResponse(statusCode);
        var result = new
        {
            success = false,
            message = message,
            correlationId = correlationId,
            timestamp = DateTime.UtcNow
        };

        response.Headers.Add("Content-Type", "application/json");
        var json = JsonSerializer.Serialize(result, JsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    #endregion

    #region CORS and Common Utilities

    /// <summary>
    /// Adds standardized CORS headers to the response
    /// Note: CORS origins are primarily handled by host.json configuration
    /// </summary>
    protected virtual void AddCorsHeaders(HttpResponseData response)
    {
        // CORS origins handled by host.json - only add additional headers if needed
        response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
        response.Headers.Add("Access-Control-Max-Age", "86400");
    }

    /// <summary>
    /// Handles OPTIONS requests for CORS preflight
    /// </summary>
    protected HttpResponseData CreateCorsResponse(HttpRequestData req)
    {
        var corsResponse = req.CreateResponse(HttpStatusCode.OK);
        AddCorsHeaders(corsResponse);
        return corsResponse;
    }

    /// <summary>
    /// Generates a new correlation ID for request tracking
    /// </summary>
    protected static string GenerateCorrelationId() => Guid.NewGuid().ToString();

    #endregion
}
