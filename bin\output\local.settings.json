{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",

    "EntraExternalID:TenantId": "REPLACE_WITH_YOUR_ACTUAL_TENANT_ID",
    "EntraExternalID:ClientId": "REPLACE_WITH_YOUR_ACTUAL_CLIENT_ID",
    "EntraExternalID:ClientSecret": "REPLACE_WITH_YOUR_ACTUAL_CLIENT_SECRET",

    "StorageConnectionString": "UseDevelopmentStorage=true",

    "PasswordHistory:MaxCount": "12",
    "PasswordHistory:WorkFactor": "12",

    "RateLimit:MaxRequestsPerMinute": "60",

    "KeyVaultUrl": "",

    "GraphApiEndpoint": "https://graph.microsoft.com/v1.0",

    // SendGrid Configuration Options (Choose ONE approach below)

    // OPTION 1: Key Vault References (PRODUCTION - Most Secure)
    // "SendGrid:ApiKey": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-ApiKey)",
    // "SendGrid:FromEmail": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-FromEmail)",
    // "SendGrid:FromName": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-FromName)",

    // OPTION 2: Environment Variables (PRODUCTION - Secure)
    // Set these in Azure Function App Configuration, not here

    // OPTION 3: Direct Values (DEVELOPMENT ONLY - Less Secure)
    "SendGrid:ApiKey": "REPLACE_WITH_YOUR_SENDGRID_API_KEY",
    "SendGrid:FromEmail": "<EMAIL>",
    "SendGrid:FromName": "Power Pages Application",

    // Application Configuration
    "ApplicationName": "Power Pages Application",
    "ResetPasswordBaseUrl": "https://your-power-pages-site.com"
  },
  "ConnectionStrings": {
    "StorageConnectionString": "UseDevelopmentStorage=true"
  },
  "Host": {
    "LocalHttpPort": 7071,
    "CORS": "*",
    "CORSCredentials": false
  }
}
