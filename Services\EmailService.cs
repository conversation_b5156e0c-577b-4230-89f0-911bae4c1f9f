using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services
{
    // Email service using SMTP2GO
    public interface IEmailService
    {
        Task<bool> SendPasswordResetEmailAsync(string toEmail, string resetToken, string verificationCode, string correlationId, CancellationToken cancellationToken = default);
        Task<bool> SendPasswordChangedNotificationAsync(string toEmail, string correlationId, CancellationToken cancellationToken = default);
    }

    public class EmailService : IEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISendGridClient? _sendGridClient;
        private readonly string? _apiKey;
        private readonly string _fromEmail;
        private readonly string _resetBaseUrl;
        private readonly string _applicationName;
        private readonly string _fromName;

        public EmailService(ILogger<EmailService> logger, IConfiguration configuration, IOptions<SendGridOptions> sendGridOptions, IOptions<PasswordResetOptions> passwordResetOptions)
        {
            _logger = logger;
            _configuration = configuration;

            // Get SendGrid configuration using strongly-typed options only
            var sendGridOpts = sendGridOptions.Value;
            _apiKey = sendGridOpts.ApiKey;

            if (string.IsNullOrEmpty(_apiKey) || _apiKey == "REPLACE_WITH_YOUR_SENDGRID_API_KEY")
            {
                _logger.LogWarning("EMAIL SERVICE DISABLED: SendGrid API key not configured. Email functionality will be bypassed. Configure SendGridApiKey for email support.");
                _sendGridClient = null; // Will be used to detect bypass mode
            }
            else
            {
                _sendGridClient = new SendGridClient(_apiKey);
                _logger.LogInformation("SendGrid client initialized with API key: {ApiKeyPrefix}... (Length: {KeyLength})",
                    _apiKey[..Math.Min(10, _apiKey.Length)], _apiKey.Length);

                // Validate API key format
                if (!_apiKey.StartsWith("SG."))
                {
                    _logger.LogWarning("API Key format warning: Expected format 'SG.xxxxx' but got '{KeyPrefix}...'",
                        _apiKey[..Math.Min(6, _apiKey.Length)]);
                }
            }

            // Get essential email configuration using strongly-typed options only
            var passwordResetOpts = passwordResetOptions.Value;
            _fromEmail = sendGridOpts.FromEmail;
            if (string.IsNullOrEmpty(_fromEmail))
            {
                throw new InvalidOperationException("SendGrid:FromEmail is required in configuration. Please configure this value in local.settings.json.");
            }
            
            _resetBaseUrl = passwordResetOpts.BaseUrl;
            if (string.IsNullOrEmpty(_resetBaseUrl))
            {
                throw new InvalidOperationException("PasswordReset:BaseUrl is required in configuration. Please configure this value in local.settings.json.");
            }

            // Initialize application name and from name with defaults
            _applicationName = "Password Reset Service";
            _fromName = "Password Reset Service";

            // Log essential configuration for debugging
            _logger.LogInformation("CONFIG DEBUG - FromEmail: {FromEmail}, ResetBaseUrl: {ResetBaseUrl}",
                _fromEmail, _resetBaseUrl);

            if (_sendGridClient != null)
            {
                _logger.LogInformation("EmailService initialized with SendGrid integration. From email: {FromEmail}", _fromEmail);
            }
            else
            {
                _logger.LogInformation("EmailService initialized in BYPASS MODE (no email sending). From email would be: {FromEmail}", _fromEmail);
            }
        }

        public async Task<bool> SendPasswordResetEmailAsync(string toEmail, string resetToken, string verificationCode, string correlationId, CancellationToken cancellationToken = default)
        {
            try
            {
                // Check if email service is in bypass mode
                if (_sendGridClient == null)
                {
                    var bypassResetLink = $"{_resetBaseUrl}?token={resetToken}";
                    _logger.LogWarning("EMAIL BYPASS: Password reset email would be sent to {Email} with reset link: {ResetLink} [CorrelationId: {CorrelationId}] - Configure SendGrid to enable actual email sending",
                        toEmail, bypassResetLink, correlationId);
                    return true; // Return success to allow password flow to continue
                }

                _logger.LogInformation("Sending password reset email to {Email} [CorrelationId: {CorrelationId}]", toEmail, correlationId);

                var resetLink = $"{_resetBaseUrl}?token={resetToken}";
                var subject = "Password Reset Request";

                // Debug the reset parameters
                _logger.LogInformation("RESET LINK DEBUG - ResetLink: {ResetLink}, VerificationCode: {VerificationCode}, Token length: {TokenLength} [CorrelationId: {CorrelationId}]",
                    resetLink, verificationCode, resetToken?.Length ?? 0, correlationId);

                var htmlContent = await GeneratePasswordResetEmailTemplate(resetLink, verificationCode, correlationId);
                var plainTextContent = GeneratePasswordResetPlainText(resetLink, verificationCode, correlationId);

                var from = new EmailAddress(_fromEmail, _fromName);
                var to = new EmailAddress(toEmail);
                var message = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);

                // Add custom headers
                message.AddCustomArg("X-Correlation-ID", correlationId);
                message.AddCustomArg("X-Email-Type", "passwordReset");
                message.AddCustomArg("X-Application-Name", _applicationName);

                // Debug logging for SendGrid request
                _logger.LogInformation("SendGrid DEBUG - From: {FromEmail}, To: {ToEmail}, Subject: {Subject}, BodyLength: {BodyLength} chars [CorrelationId: {CorrelationId}]",
                    _fromEmail, toEmail, subject, htmlContent?.Length ?? 0, correlationId);

                var response = await _sendGridClient.SendEmailAsync(message);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Password reset email sent to {Email} [CorrelationId: {CorrelationId}]", toEmail, correlationId);
                    return true;
                }
                else
                {
                    var errorMessage = response.Body != null ? await response.Body.ReadAsStringAsync() : "Unknown error";

                    _logger.LogError("Failed to send password reset email to {Email}. StatusCode: {StatusCode}, Error: {Error} [CorrelationId: {CorrelationId}]",
                        toEmail, response.StatusCode, errorMessage, correlationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending password reset email to {Email} [CorrelationId: {CorrelationId}]", toEmail, correlationId);
                return false;
            }
        }

        public async Task<bool> SendPasswordChangedNotificationAsync(string toEmail, string correlationId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (_sendGridClient == null)
                {
                    _logger.LogWarning("EMAIL BYPASS: Password changed notification would be sent to {Email} [CorrelationId: {CorrelationId}] - Configure SendGrid to enable actual email sending",
                        toEmail, correlationId);
                    return true; // Return success to allow password flow to continue
                }

                _logger.LogInformation("Sending password changed notification to {Email} [CorrelationId: {CorrelationId}]", toEmail, correlationId);

                var subject = $"Password Changed - {_applicationName}";
                var htmlContent = await GeneratePasswordChangedEmailTemplate(correlationId);
                var plainTextContent = GeneratePasswordChangedPlainText(correlationId);

                var from = new EmailAddress(_fromEmail, _fromName);
                var to = new EmailAddress(toEmail);
                var message = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);

                // Add custom headers for tracking
                message.AddCustomArg("X-Correlation-ID", correlationId);
                message.AddCustomArg("X-Email-Type", "passwordChanged");
                message.AddCustomArg("X-Application-Name", _applicationName);

                var response = await _sendGridClient.SendEmailAsync(message);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Password changed notification sent successfully to {Email} [CorrelationId: {CorrelationId}]", toEmail, correlationId);
                    return true;
                }
                else
                {
                    var errorMessage = response.Body != null ? await response.Body.ReadAsStringAsync() : "Unknown error";

                    _logger.LogError("Failed to send password changed notification to {Email}. StatusCode: {StatusCode}, Error: {Error} [CorrelationId: {CorrelationId}]",
                        toEmail, response.StatusCode, errorMessage, correlationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending password changed notification to {Email} [CorrelationId: {CorrelationId}]", toEmail, correlationId);
                return false;
            }
        }

        private async Task<string> LoadEmailTemplate(string templateName)
        {
            try
            {
                // Try multiple path combinations to handle different execution contexts
                var possiblePaths = new[]
                {
                    Path.Combine("Services", "Templates", templateName),
                    Path.Combine(AppContext.BaseDirectory, "Services", "Templates", templateName),
                    Path.Combine(Environment.CurrentDirectory, "Services", "Templates", templateName)
                };

                string? templatePath = null;
                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        templatePath = path;
                        break;
                    }
                }

                if (templatePath == null)
                {
                    _logger.LogError("Email template not found in any of the following paths: {Paths}", string.Join(", ", possiblePaths));
                    throw new FileNotFoundException($"Email template not found: {templateName}");
                }

                _logger.LogInformation("Loading email template from: {TemplatePath}", templatePath);
                return await File.ReadAllTextAsync(templatePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading email template: {TemplateName}", templateName);
                throw;
            }
        }

        private async Task<string> GeneratePasswordResetEmailTemplate(string resetLink, string verificationCode, string correlationId)
        {
            var template = await LoadEmailTemplate("PasswordReset.html");

            return template
                .Replace("{{applicationName}}", _applicationName)
                .Replace("{{resetLink}}", resetLink)
                .Replace("{{verificationCode}}", verificationCode)
                .Replace("{{correlationId}}", correlationId);
        }

        private string GeneratePasswordResetPlainText(string resetLink, string verificationCode, string correlationId)
        {
            return $@"
{_applicationName} - Password Reset Request

You have requested to reset your password for your {_applicationName} account.

YOUR VERIFICATION CODE: {verificationCode}

To reset your password:
1. Click or copy the following link into your browser: {resetLink}
2. Enter the verification code above when prompted: {verificationCode}

IMPORTANT SECURITY INFORMATION:
- This link will expire in 15 minutes
- If you didn't request this reset, please ignore this email
- For security, this link can only be used once
- Never share this link with anyone

This is an automated message. Please do not reply to this email.
Reference ID: {correlationId}
";
        }

        private async Task<string> GeneratePasswordChangedEmailTemplate(string correlationId)
        {
            var template = await LoadEmailTemplate("PasswordChanged.html");
            var now = DateTime.UtcNow;

            return template
                .Replace("{{applicationName}}", _applicationName)
                .Replace("{{changeDate}}", now.ToString("yyyy-MM-dd"))
                .Replace("{{changeTime}}", now.ToString("HH:mm"))
                .Replace("{{correlationId}}", correlationId);
        }

        private string GeneratePasswordChangedPlainText(string correlationId)
        {
            return $@"
{_applicationName} - Password Successfully Changed

Your password for {_applicationName} has been successfully changed.

SECURITY CONFIRMATION:
- Your password change was completed successfully
- This change was made on {DateTime.UtcNow:yyyy-MM-dd} at {DateTime.UtcNow:HH:mm} UTC
- If you didn't make this change, please contact support immediately

You can now use your new password to sign in to {_applicationName}.

This is an automated message. Please do not reply to this email.
Reference ID: {correlationId}
";
        }


    }
}
