using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using System.Security.Cryptography;
using PasswordHistoryValidator.Services;

namespace PasswordHistoryValidator.Services;

/// <summary>
/// Manages password reset tokens and verification codes using in-memory cache
/// </summary>
public class ResetTokenManager
{
    private readonly ILogger<ResetTokenManager> _logger;
    private readonly IMemoryCache _cache;
    private readonly IEmailService _emailService;

    private const int TokenExpirationMinutes = 15;

    public ResetTokenManager(
        ILogger<ResetTokenManager> logger,
        IMemoryCache cache,
        IEmailService emailService)
    {
        _logger = logger;
        _cache = cache;
        _emailService = emailService;
    }

    /// <summary>
    /// Generates a cryptographically secure reset token
    /// </summary>
    public string GenerateSecureToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var tokenBytes = new byte[32];
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    /// <summary>
    /// Generates a cryptographically secure 6-digit verification code
    /// </summary>
    public string GenerateVerificationCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var code = (randomNumber % 900000) + 100000;
        return code.ToString();
    }

    /// <summary>
    /// Stores a reset token with verification code in memory cache
    /// </summary>
    public string StoreResetToken(string applicationId, string email, string token)
    {
        try
        {
            var verificationCode = GenerateVerificationCode();
            var cacheKey = $"reset_token_{token}";

            var tokenData = new ResetTokenData
            {
                Email = email,
                ApplicationId = applicationId,
                Token = token,
                VerificationCode = verificationCode,
                CreatedUtc = DateTime.UtcNow,
                ExpiresUtc = DateTime.UtcNow.AddMinutes(TokenExpirationMinutes),
                Used = false
            };

            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(TokenExpirationMinutes)
            };

            _cache.Set(cacheKey, tokenData, cacheOptions);
            _logger.LogDebug("Reset token stored in cache for {Email}", email);

            return verificationCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing reset token for {Email}", email);
            throw;
        }
    }

    /// <summary>
    /// Validates a reset token and verification code
    /// </summary>
    public (bool IsValid, ResetTokenData? TokenData, string ErrorMessage) ValidateResetToken(
        string token, string verificationCode)
    {
        try
        {
            var cacheKey = $"reset_token_{token}";
            
            if (!_cache.TryGetValue(cacheKey, out ResetTokenData? tokenData) || tokenData == null)
            {
                return (false, null, "Invalid or expired reset token");
            }

            // Check if already used
            if (tokenData.Used)
            {
                return (false, null, "Reset token has already been used");
            }

            // Check if expired (redundant with cache TTL, but good for explicit validation)
            if (DateTime.UtcNow > tokenData.ExpiresUtc)
            {
                return (false, null, "Reset token has expired");
            }

            // Check verification code
            if (tokenData.VerificationCode != verificationCode)
            {
                return (false, null, "Invalid verification code");
            }

            // Token is valid
            return (true, tokenData, string.Empty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating reset token");
            return (false, null, "Error validating reset token");
        }
    }

    /// <summary>
    /// Marks a reset token as used
    /// </summary>
    public bool MarkTokenAsUsed(string token)
    {
        try
        {
            var cacheKey = $"reset_token_{token}";
            
            if (_cache.TryGetValue(cacheKey, out ResetTokenData? tokenData) && tokenData != null)
            {
                // Mark as used
                tokenData.Used = true;
                tokenData.UsedUtc = DateTime.UtcNow;

                // Update in cache
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(TokenExpirationMinutes)
                };
                _cache.Set(cacheKey, tokenData, cacheOptions);

                _logger.LogDebug("Reset token marked as used for {Email}", tokenData.Email);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking reset token as used");
            return false;
        }
    }

    /// <summary>
    /// Sends password reset email with token and verification code
    /// </summary>
    public async Task SendResetEmail(string email, string token, string verificationCode, string applicationId, string correlationId)
    {
        try
        {
            _logger.LogInformation("Sending password reset email to {Email} [CorrelationId: {CorrelationId}]", email, correlationId);

            // Send email using SendGrid service with both token and verification code
            var emailSent = await _emailService.SendPasswordResetEmailAsync(email, token, verificationCode, correlationId);

            if (emailSent)
            {
                _logger.LogInformation("Password reset email sent successfully to {Email} [CorrelationId: {CorrelationId}]", email, correlationId);
            }
            else
            {
                _logger.LogError("Failed to send password reset email to {Email} [CorrelationId: {CorrelationId}]", email, correlationId);
                // Note: We don't throw here to avoid exposing email service failures to the user
                // The forgot password flow will still complete successfully from the user's perspective
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending reset email to {Email} [CorrelationId: {CorrelationId}]", email, correlationId);
            // Note: We don't re-throw to avoid exposing email service failures to the user
            // The forgot password flow will still complete successfully from the user's perspective
        }
    }

    // Note: Cleanup method removed - IMemoryCache handles expiration automatically
}
