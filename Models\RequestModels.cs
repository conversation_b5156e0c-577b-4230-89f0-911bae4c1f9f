using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PasswordHistoryValidator;

// Unified model for user operations (register, login, credential validation)
public class UserOperationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;

    // For registration only
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Department { get; set; }
    public string? UserRole { get; set; }
    public string? RegistrationSource { get; set; } = "PowerPages";

    // For login only
    public bool RememberMe { get; set; } = false;

    // For credential validation
    public string? UserId { get; set; }
    public string? CurrentPassword { get; set; }
    public bool UpdateHistory { get; set; } = false;
}

// Unified model for password operations (reset, change, validation)
public class PasswordOperationRequest
{
    // User identification (email preferred, userId fallback)
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string? Email { get; set; }
    public string? UserId { get; set; }

    [Required(ErrorMessage = "New password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;

    // For password change (current password required)
    public string? CurrentPassword { get; set; }

    // For token-based reset
    public string? Token { get; set; }
    public string? VerificationCode { get; set; }

    public bool UpdateHistory { get; set; } = true;
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

// Simplified model for forgot password flow
public class ForgotPasswordRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;

    // For completion step
    public string? Token { get; set; }
    public string? VerificationCode { get; set; }
    public string? NewPassword { get; set; }

    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

// Simple validation request for password checks
public class ValidationRequest
{
    [Required(ErrorMessage = "User ID is required")]
    public string UserId { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;

    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

// Token data for in-memory cache
public class ResetTokenData
{
    public string Email { get; set; } = string.Empty;
    public string ApplicationId { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public string VerificationCode { get; set; } = string.Empty;
    public DateTime CreatedUtc { get; set; }
    public DateTime ExpiresUtc { get; set; }
    public bool Used { get; set; }
    public DateTime? UsedUtc { get; set; }
}


