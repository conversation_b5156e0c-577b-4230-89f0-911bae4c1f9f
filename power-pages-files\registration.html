<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">

<!-- Secure configuration and user data extraction -->
<script>
  // Secure configuration management - get from Power Pages settings
  window.appConfig = {
    functionUrl: {{ settings['AzureFunctionUrl'] | default: 'ERROR_MISSING_AzureFunctionUrl' | json }},
    msalClientId: {{ settings['MSALClientId'] | default: 'ERROR_MISSING_MSALClientId' | json }},
    msalTenantId: {{ settings['MSALTenantId'] | default: 'ERROR_MISSING_MSALTenantId' | json }},
    applicationName: {{ settings['ApplicationName'] | default: 'ERROR_MISSING_ApplicationName' | json }}, // Enhanced: Application name
    entraTenantDomain: {{ settings['EntraTenantDomain'] | default: 'ERROR_MISSING_EntraTenantDomain' | json }}, // Configurable tenant domain
    authProvider: {{ settings['AuthProvider'] | default: 'EntraExternalID' | json }}, // Power Pages authentication provider name
  };

  // Debug logging for configuration
  console.log("Power Pages Configuration Debug:", {
    rawApplicationName: "{{ settings['ApplicationName'] }}",
    rawMSALTenantId: "{{ settings['MSALTenantId'] }}",
    rawMSALClientId: "{{ settings['MSALClientId'] }}",
    rawEntraTenantDomain: "{{ settings['EntraTenantDomain'] }}",
    finalApplicationName: window.appConfig.applicationName,
    entraTenantDomain: window.appConfig.entraTenantDomain,
    msalTenantId: window.appConfig.msalTenantId,
    msalClientId: window.appConfig.msalClientId ? "configured" : "missing",
    allSettings: {
      ApplicationName: "{{ settings['ApplicationName'] }}",
      MSALTenantId: "{{ settings['MSALTenantId'] }}",
      MSALClientId: "{{ settings['MSALClientId'] }}",
      EntraTenantDomain: "{{ settings['EntraTenantDomain'] }}",
      AuthProvider: "{{ settings['AuthProvider'] }}"
    }
  });

  // Secure user data extraction with input sanitization
  function sanitizeUserInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  }

  // Secure configuration helper
  window.SecureConfig = {
    getFunctionUrl: function(functionName) {
      const baseUrl = window.appConfig.functionUrl;
      if (!baseUrl) {
        console.error('Azure Function URL not configured');
        return null;
      }
      return `${baseUrl}/api/${functionName}`;
    },
    getMSALConfig: function() {
      return {
        clientId: window.appConfig.msalClientId,
        tenantId: window.appConfig.msalTenantId,
        authority: `https://login.microsoftonline.com/${window.appConfig.msalTenantId}`
      };
    },
    getApplicationName: function() {
      return window.appConfig.applicationName;
    }
  };
</script>

<!-- User Registration Form -->
<div class="container mt-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="text-center">Create Account</h3>
        </div>
        <div class="card-body">
          <!-- Registration Form -->
          <form id="registrationForm">
            <!-- Email Field -->
            <div class="form-group mb-3">
              <label for="email" class="form-label fw-bold">Email Address</label>
              <input
                type="email"
                id="email"
                required
                class="form-control"
                placeholder="Enter your email address"
              />
              <div class="invalid-feedback" id="emailError"></div>
            </div>

            <!-- First Name -->
            <div class="form-group mb-3">
              <label for="firstName" class="form-label fw-bold">First Name</label>
              <input
                type="text"
                id="firstName"
                required
                class="form-control"
                placeholder="Enter your first name"
              />
              <div class="invalid-feedback" id="firstNameError"></div>
            </div>

            <!-- Last Name -->
            <div class="form-group mb-3">
              <label for="lastName" class="form-label fw-bold">Last Name</label>
              <input
                type="text"
                id="lastName"
                required
                class="form-control"
                placeholder="Enter your last name"
              />
              <div class="invalid-feedback" id="lastNameError"></div>
            </div>

            <!-- Password Field -->
            <div class="form-group mb-3">
              <label for="password" class="form-label fw-bold">Password</label>
              <div class="input-group">
                <input
                  type="password"
                  id="password"
                  required
                  class="form-control"
                  placeholder="Enter your password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="togglePassword"
                  title="Toggle password visibility"
                  aria-label="Toggle password visibility"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="form-text">
                Password must be at least 8 characters with uppercase,
                lowercase, number, and special character.
              </div>
              <div class="invalid-feedback" id="passwordError"></div>
            </div>

            <!-- Confirm Password -->
            <div class="form-group mb-3">
              <label for="confirmPassword" class="form-label fw-bold">Confirm Password</label>
              <div class="input-group">
                <input
                  type="password"
                  id="confirmPassword"
                  required
                  class="form-control"
                  placeholder="Confirm your password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="toggleConfirmPassword"
                  title="Toggle confirm password visibility"
                  aria-label="Toggle confirm password visibility"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="invalid-feedback" id="confirmPasswordError"></div>
            </div>

            <!-- Terms and Conditions -->
            <div class="form-check mb-3">
              <input
                type="checkbox"
                id="termsAccepted"
                required
                class="form-check-input"
              />
              <label for="termsAccepted" class="form-check-label">
                I agree to the
                <a href="/terms" target="_blank">Terms and Conditions</a>
              </label>
              <div class="invalid-feedback" id="termsError"></div>
            </div>

            <!-- Submit Button -->
            <button
              type="submit"
              id="registerButton"
              class="btn btn-primary w-100"
            >
              Create Account
            </button>
          </form>

          <!-- Messages -->
          <div id="errorMessage" class="alert alert-danger mt-3 d-none"></div>
          <div id="successMessage" class="alert alert-success mt-3 d-none"></div>
          <div id="messageContainer" class="mt-3 hidden">
            <div id="messageContent" class="alert"></div>
          </div>

          <!-- Login Link -->
          <div class="text-center mt-3">
            <p>Already have an account? <a href="/" class="btn btn-outline-primary">Sign in with your existing account</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include Registration JavaScript -->
<script src="registration.js"></script>
