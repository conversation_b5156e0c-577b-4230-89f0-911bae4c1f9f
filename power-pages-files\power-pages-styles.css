/* Power Pages Custom Styles - Osler Branding */

/* ========================================
   OSLER BRAND COLORS & VARIABLES
   ======================================== */
:root {
    --osler-red: #dc2626;
    --osler-red-dark: #b91c1c;
    --osler-red-light: #fef2f2;
    --osler-black: #000000;
    --osler-white: #ffffff;
    --osler-gray-dark: #374151;
    --osler-gray-medium: #6b7280;
    --osler-gray-light: #f3f4f6;
    --osler-gray-border: #e5e7eb;

    /* Typography */
    --osler-font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --osler-font-secondary: 'Arial', sans-serif;

    /* Spacing */
    --osler-spacing-xs: 0.5rem;
    --osler-spacing-sm: 1rem;
    --osler-spacing-md: 1.5rem;
    --osler-spacing-lg: 2rem;
    --osler-spacing-xl: 3rem;

    /* Border radius */
    --osler-radius-sm: 4px;
    --osler-radius-md: 6px;
    --osler-radius-lg: 8px;

    /* Shadows */
    --osler-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --osler-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --osler-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ========================================
   GLOBAL STYLES
   ======================================== */
body {
    font-family: var(--osler-font-primary);
    color: var(--osler-gray-dark);
    line-height: 1.6;
    background-color: var(--osler-gray-light);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--osler-black);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--osler-spacing-sm);
}

h1 {
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
    margin-bottom: var(--osler-spacing-md);
}

h2 {
    font-size: 2rem;
    margin-bottom: var(--osler-spacing-md);
}

h3 {
    font-size: 1.5rem;
    color: var(--osler-red);
}

p {
    margin-bottom: var(--osler-spacing-sm);
    color: var(--osler-gray-dark);
}

/* Links */
a {
    color: var(--osler-red);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--osler-red-dark);
    text-decoration: underline;
}

/* ========================================
   LAYOUT CONTAINERS
   ======================================== */
.columnBlockLayout {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-width: 250px;
    padding: var(--osler-spacing-lg);
    margin: var(--osler-spacing-xl) 0;
    background-color: var(--osler-white);
    border-radius: var(--osler-radius-lg);
    box-shadow: var(--osler-shadow-md);
}

.sectionBlockLayout {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    min-height: auto;
    padding: var(--osler-spacing-sm);
    background-color: var(--osler-white);
}

.container-flex {
    display: flex;
    flex-wrap: wrap;
    gap: var(--osler-spacing-md);
}

/* ========================================
   PAGE-SPECIFIC CONTAINERS
   ======================================== */
.forgot-password-container,
.login-container,
.reset-password-container {
    max-width: 600px;
    margin: var(--osler-spacing-lg) auto;
    padding: var(--osler-spacing-xl);
    background-color: var(--osler-white);
    border-radius: var(--osler-radius-lg);
    box-shadow: var(--osler-shadow-lg);
    border-top: 4px solid var(--osler-red);
}

/* Header styling for auth containers */
.forgot-password-container h1,
.login-container h1,
.reset-password-container h1 {
    text-align: center;
    margin-bottom: var(--osler-spacing-sm);
    position: relative;
}

.forgot-password-container h1::after,
.login-container h1::after,
.reset-password-container h1::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--osler-red);
    margin: var(--osler-spacing-sm) auto;
}

/* ========================================
   FORM ELEMENTS
   ======================================== */
.form-group {
    margin-bottom: var(--osler-spacing-md);
}

label {
    display: block;
    font-weight: 600;
    color: var(--osler-black);
    margin-bottom: var(--osler-spacing-xs);
    font-size: 0.95rem;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
textarea,
select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--osler-gray-border);
    border-radius: var(--osler-radius-md);
    font-size: 1rem;
    font-family: var(--osler-font-primary);
    background-color: var(--osler-white);
    color: var(--osler-gray-dark);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--osler-red);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

input[type="text"]:invalid,
input[type="email"]:invalid,
input[type="password"]:invalid {
    border-color: var(--osler-red);
}

/* ========================================
   BUTTONS
   ======================================== */
.btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--osler-radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--osler-font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 120px;
}

/* Primary button (Osler red) */
.btn-primary,
button[type="submit"],
input[type="submit"] {
    background-color: var(--osler-red);
    color: var(--osler-white);
    border: 2px solid var(--osler-red);
}

.btn-primary:hover,
button[type="submit"]:hover,
input[type="submit"]:hover {
    background-color: var(--osler-red-dark);
    border-color: var(--osler-red-dark);
    color: var(--osler-white);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--osler-shadow-md);
}

/* Secondary button (outline) */
.btn-secondary {
    background-color: transparent;
    color: var(--osler-red);
    border: 2px solid var(--osler-red);
}

.btn-secondary:hover {
    background-color: var(--osler-red);
    color: var(--osler-white);
    text-decoration: none;
}

/* Tertiary button (minimal) */
.btn-tertiary {
    background-color: transparent;
    color: var(--osler-gray-dark);
    border: 2px solid var(--osler-gray-border);
}

.btn-tertiary:hover {
    background-color: var(--osler-gray-light);
    border-color: var(--osler-gray-medium);
    color: var(--osler-gray-dark);
    text-decoration: none;
}

/* Button sizes */
.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
    min-width: 100px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.125rem;
    min-width: 150px;
}

/* Full width button */
.btn-block {
    width: 100%;
    display: block;
}

/* ========================================
   MESSAGES & ALERTS
   ======================================== */
.message,
.alert {
    padding: var(--osler-spacing-md);
    border-radius: var(--osler-radius-md);
    margin-bottom: var(--osler-spacing-md);
    border-left: 4px solid;
    font-weight: 500;
}

/* Success messages */
.message-success,
.alert-success {
    background-color: #f0f9ff;
    border-left-color: var(--osler-red);
    color: var(--osler-gray-dark);
}

/* Error messages */
.message-error,
.alert-error,
.alert-danger {
    background-color: var(--osler-red-light);
    border-left-color: var(--osler-red);
    color: var(--osler-red-dark);
}

/* Warning messages */
.message-warning,
.alert-warning {
    background-color: #fef3c7;
    border-left-color: #f59e0b;
    color: #92400e;
}

/* Info messages */
.message-info,
.alert-info {
    background-color: #eff6ff;
    border-left-color: #3b82f6;
    color: #1e40af;
}

/* Hidden messages */
.message-hidden {
    display: none;
}

/* ========================================
   CARDS & PANELS
   ======================================== */
.card {
    background-color: var(--osler-white);
    border-radius: var(--osler-radius-lg);
    box-shadow: var(--osler-shadow-md);
    overflow: hidden;
    margin-bottom: var(--osler-spacing-md);
}

.card-header {
    padding: var(--osler-spacing-md);
    background-color: var(--osler-gray-light);
    border-bottom: 1px solid var(--osler-gray-border);
    font-weight: 600;
    color: var(--osler-black);
}

.card-body {
    padding: var(--osler-spacing-md);
}

.card-footer {
    padding: var(--osler-spacing-md);
    background-color: var(--osler-gray-light);
    border-top: 1px solid var(--osler-gray-border);
    text-align: center;
}

/* ========================================
   VERIFICATION CODE STYLING
   ======================================== */
.verification-code-container {
    background-color: var(--osler-gray-light);
    border-left: 4px solid var(--osler-red);
    padding: var(--osler-spacing-lg);
    margin: var(--osler-spacing-lg) 0;
    text-align: center;
    border-radius: var(--osler-radius-md);
}

.verification-code {
    background-color: var(--osler-white);
    border: 2px solid var(--osler-red);
    border-radius: var(--osler-radius-md);
    padding: var(--osler-spacing-md);
    margin: var(--osler-spacing-sm) 0;
    font-family: 'Courier New', monospace;
    font-size: 2rem;
    font-weight: bold;
    color: var(--osler-red);
    letter-spacing: 6px;
    display: inline-block;
    min-width: 200px;
}

/* ========================================
   UTILITY CLASSES
   ======================================== */
.flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--osler-spacing-md);
}

.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-red {
    color: var(--osler-red);
}

.text-black {
    color: var(--osler-black);
}

.text-gray {
    color: var(--osler-gray-medium);
}

.bg-red {
    background-color: var(--osler-red);
}

.bg-white {
    background-color: var(--osler-white);
}

.bg-gray-light {
    background-color: var(--osler-gray-light);
}

/* Spacing utilities */
.mt-sm { margin-top: var(--osler-spacing-sm); }
.mt-md { margin-top: var(--osler-spacing-md); }
.mt-lg { margin-top: var(--osler-spacing-lg); }
.mb-sm { margin-bottom: var(--osler-spacing-sm); }
.mb-md { margin-bottom: var(--osler-spacing-md); }
.mb-lg { margin-bottom: var(--osler-spacing-lg); }
.pt-sm { padding-top: var(--osler-spacing-sm); }
.pt-md { padding-top: var(--osler-spacing-md); }
.pt-lg { padding-top: var(--osler-spacing-lg); }
.pb-sm { padding-bottom: var(--osler-spacing-sm); }
.pb-md { padding-bottom: var(--osler-spacing-md); }
.pb-lg { padding-bottom: var(--osler-spacing-lg); }

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */
@media (max-width: 768px) {
    .columnBlockLayout {
        padding: var(--osler-spacing-md);
        margin: var(--osler-spacing-md) 0;
    }

    .forgot-password-container,
    .login-container,
    .reset-password-container {
        margin: var(--osler-spacing-sm) auto;
        padding: var(--osler-spacing-md);
        max-width: 95%;
    }

    h1 {
        font-size: 2rem;
        letter-spacing: 1px;
    }

    h2 {
        font-size: 1.5rem;
    }

    .verification-code {
        font-size: 1.5rem;
        letter-spacing: 4px;
        min-width: 150px;
    }

    .btn {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .container-flex {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .forgot-password-container,
    .login-container,
    .reset-password-container {
        padding: var(--osler-spacing-sm);
        margin: var(--osler-spacing-xs) auto;
    }

    h1 {
        font-size: 1.75rem;
    }

    .verification-code {
        font-size: 1.25rem;
        letter-spacing: 2px;
        min-width: 120px;
    }
}

/* ========================================
   POWER PAGES SPECIFIC OVERRIDES
   ======================================== */

/* Override default Power Pages form styling */
.entityform .form-group {
    margin-bottom: var(--osler-spacing-md);
}

.entityform label {
    font-weight: 600;
    color: var(--osler-black);
    margin-bottom: var(--osler-spacing-xs);
}

.entityform input[type="text"],
.entityform input[type="email"],
.entityform input[type="password"],
.entityform textarea,
.entityform select {
    border: 2px solid var(--osler-gray-border);
    border-radius: var(--osler-radius-md);
    padding: 12px 16px;
}

.entityform input[type="text"]:focus,
.entityform input[type="email"]:focus,
.entityform input[type="password"]:focus,
.entityform textarea:focus,
.entityform select:focus {
    border-color: var(--osler-red);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Power Pages navigation styling */
.navbar-brand {
    color: var(--osler-black) !important;
    font-weight: 300;
    letter-spacing: 2px;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: var(--osler-gray-dark) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--osler-red) !important;
}

/* Power Pages footer styling */
.footer {
    background-color: var(--osler-black);
    color: var(--osler-white);
    padding: var(--osler-spacing-lg) 0;
    margin-top: var(--osler-spacing-xl);
}

.footer a {
    color: var(--osler-white);
}

.footer a:hover {
    color: var(--osler-red);
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
*:focus {
    outline: 2px solid var(--osler-red);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --osler-red: #cc0000;
        --osler-gray-border: #666666;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
