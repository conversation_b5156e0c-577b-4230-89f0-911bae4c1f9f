using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PasswordHistoryValidator.Services;

// Password history operations service
public interface IPasswordHistoryService
{
    // Legacy methods (single-tenant)
    Task<Result<List<string>>> GetPasswordHistoryAsync(string userId, CancellationToken cancellationToken = default);
    Task<Result<bool>> UpdatePasswordHistoryAsync(string userId, string newPassword, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string userId, string password, CancellationToken cancellationToken = default);

    // Application-aware methods (multi-tenant)
    Task<Result<List<string>>> GetPasswordHistoryAsync(string applicationId, string userId, CancellationToken cancellationToken = default);
    Task<Result<bool>> UpdatePasswordHistoryAsync(string applicationId, string userId, string newPassword, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string applicationId, string userId, string password, CancellationToken cancellationToken = default);
}

// Simplified Result pattern for structured error handling
public class Result<T>
{
    public bool IsSuccess { get; private set; }
    public T? Value { get; private set; }
    public string ErrorMessage { get; private set; } = string.Empty;
    public string? ErrorCode { get; private set; }

    public static Result<T> Success(T value) => new()
    {
        IsSuccess = true,
        Value = value
    };

    public static Result<T> Failure(string errorMessage) => new()
    {
        IsSuccess = false,
        ErrorMessage = errorMessage
    };

    public static Result<T> Failure(string errorMessage, string errorCode) => new()
    {
        IsSuccess = false,
        ErrorMessage = errorMessage,
        ErrorCode = errorCode
    };

    // Overload for backward compatibility - correlationId is ignored as it's not part of Result
    public static Result<T> Failure(string errorMessage, string errorCode, string correlationId) => new()
    {
        IsSuccess = false,
        ErrorMessage = errorMessage,
        ErrorCode = errorCode
    };
}

// Error codes used throughout the application
public static class ErrorCodes
{
    public const string InvalidRequest = "INVALID_REQUEST";
    public const string PasswordInHistory = "PASSWORD_IN_HISTORY";
    public const string StorageError = "STORAGE_ERROR";
    public const string ConfigurationError = "CONFIGURATION_ERROR";
    public const string ValidationError = "VALIDATION_ERROR";
    public const string RateLimitExceeded = "RATE_LIMIT_EXCEEDED";
    public const string UnauthorizedAccess = "UNAUTHORIZED_ACCESS";
    public const string UserNotFound = "USER_NOT_FOUND";
    public const string InternalError = "INTERNAL_ERROR";
    public const string AuthenticationFailed = "AUTHENTICATION_FAILED";
    public const string UserAlreadyExists = "USER_ALREADY_EXISTS";
    public const string InvalidToken = "INVALID_TOKEN";
    public const string TokenExpired = "TOKEN_EXPIRED";
    public const string EmailSendFailed = "EMAIL_SEND_FAILED";
}

// Models that are still being used by specific functions
public class UpdatePasswordHistoryRequest
{
    [JsonPropertyName("email")]
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public required string Email { get; set; }

    [JsonPropertyName("newPassword")]
    [Required(ErrorMessage = "New password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public required string NewPassword { get; set; }

    [JsonPropertyName("userId")]
    public string? UserId { get; set; }

    [JsonPropertyName("applicationName")]
    [Required(ErrorMessage = "Application name is required")]
    public required string ApplicationName { get; set; }

    [JsonPropertyName("correlationId")]
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

public class AuthenticateUserRequest
{
    [JsonPropertyName("email")]
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public required string Email { get; set; }

    [JsonPropertyName("password")]
    [Required(ErrorMessage = "Password is required")]
    public required string Password { get; set; }

    [JsonPropertyName("applicationName")]
    [Required(ErrorMessage = "Application name is required")]
    public required string ApplicationName { get; set; }

    [JsonPropertyName("rememberMe")]
    public bool RememberMe { get; set; } = false;

    [JsonPropertyName("correlationId")]
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}


