using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Text.Json;
using Azure.Storage.Blobs;
using PasswordHistoryValidator.Services;
using System.Net.Http.Headers;
using System.Text;

namespace PasswordHistoryValidator.Services;

/// <summary>
/// Handles password change operations and validation against history
/// </summary>
public class PasswordOperations
{
    private readonly ILogger<PasswordOperations> _logger;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IEmailService _emailService;
    private readonly JsonSerializerOptions _jsonOptions;

    public PasswordOperations(
        ILogger<PasswordOperations> logger,
        GraphServiceClient graphServiceClient,
        BlobServiceClient blobServiceClient,
        IEmailService emailService,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger;
        _graphServiceClient = graphServiceClient;
        _blobServiceClient = blobServiceClient;
        _emailService = emailService;
        _jsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
    }

    /// <summary>
    /// Changes user password using Graph API with current password validation
    /// </summary>
    public async Task<bool> ChangePasswordWithGraphApi(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken)
    {
        try
        {
            var currentPasswordValid = await ValidateCurrentPasswordAgainstHistory(userId, currentPassword, cancellationToken);
            if (!currentPasswordValid)
            {
                return false;
            }

            var user = new User
            {
                PasswordProfile = new PasswordProfile
                {
                    Password = newPassword,
                    ForceChangePasswordNextSignIn = false
                }
            };

            await _graphServiceClient.Users[userId].PatchAsync(user, requestConfiguration =>
            {
            }, cancellationToken);

            await SendPasswordChangedNotification(userId, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password change error for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Enhanced password change with Entra External ID compatibility
    /// </summary>
    public async Task<(bool Success, string ErrorMessage)> ChangePasswordWithEntraExternalIdCompliance(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Attempting Entra External ID compliant password change for user {UserId}", userId);

            var currentPasswordValid = await ValidateCurrentPasswordAgainstHistory(userId, currentPassword, cancellationToken);
            if (!currentPasswordValid)
            {
                _logger.LogWarning("Current password validation failed for user {UserId}", userId);
                return (false, "Current password is incorrect. Please verify your current password and try again.");
            }

            _logger.LogInformation("Current password validated successfully for user {UserId}", userId);

            // Create the password update request
            var user = new User
            {
                PasswordProfile = new PasswordProfile
                {
                    Password = newPassword,
                    ForceChangePasswordNextSignIn = false
                }
            };

            // Attempt password change via Graph API
            await _graphServiceClient.Users[userId].PatchAsync(user, requestConfiguration =>
            {
                // Add any specific request configuration if needed
            }, cancellationToken);

            _logger.LogInformation("Password changed successfully via Graph API for user {UserId}", userId);

            // Send notification email
            await SendPasswordChangedNotification(userId, cancellationToken);

            return (true, "Password changed successfully");
        }
        catch (HttpRequestException httpEx)
        {
            _logger.LogError(httpEx, "HTTP error during password change for user {UserId}: {Message}", userId, httpEx.Message);
            return (false, "Network error occurred while changing password. Please check your connection and try again.");
        }
        catch (TaskCanceledException tcEx)
        {
            _logger.LogError(tcEx, "Timeout during password change for user {UserId}", userId);
            return (false, "Password change request timed out. Please try again.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during password change for user {UserId}: {Message}", userId, ex.Message);
            return (false, "An unexpected error occurred. Please try again later or contact support.");
        }
    }

    /// <summary>
    /// Resets user password using Graph API
    /// </summary>
    public async Task<bool> ResetPasswordWithGraphApi(string email, string newPassword, CancellationToken cancellationToken)
    {
        try
        {
            // Get user by email
            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = $"mail eq '{email}'";
                }, cancellationToken);

            if (users?.Value?.Any() != true)
            {
                _logger.LogError("User not found for email {Email}", email);
                return false;
            }

            var user = users.Value.First();

            // Update user password
            var userUpdate = new User
            {
                PasswordProfile = new PasswordProfile
                {
                    Password = newPassword,
                    ForceChangePasswordNextSignIn = false
                }
            };

            await _graphServiceClient.Users[user.Id].PatchAsync(userUpdate, requestConfiguration =>
            {
                // Configure request if needed
            }, cancellationToken);

            _logger.LogInformation("Password successfully reset for user {Email}", email);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for user {Email}", email);
            return false;
        }
    }

    /// <summary>
    /// Validates current password against stored password history
    /// </summary>
    private async Task<bool> ValidateCurrentPasswordAgainstHistory(string userId, string currentPassword, CancellationToken cancellationToken)
    {
        try
        {
            var historyResult = await GetMostRecentPasswordHashForUser(userId, cancellationToken);

            if (!historyResult.IsSuccess || string.IsNullOrEmpty(historyResult.Value))
            {
                return true;
            }

            var isCurrentPasswordValid = BCrypt.Net.BCrypt.Verify(currentPassword, historyResult.Value);
            return isCurrentPasswordValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Current password validation error for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Gets the most recent password hash for a user from blob storage
    /// </summary>
    private async Task<Result<string>> GetMostRecentPasswordHashForUser(string userId, CancellationToken cancellationToken)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("passwordhistory");

            string mostRecentHash = string.Empty;
            DateTime mostRecentDate = DateTime.MinValue;

            await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                if (blobItem.Name.EndsWith($"_{userId}.json") || blobItem.Name.EndsWith($"/{userId}.json"))
                {
                    try
                    {
                        var blobClient = containerClient.GetBlobClient(blobItem.Name);
                        var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                        var historyJson = downloadResult.Value.Content.ToString();

                        var historyData = JsonSerializer.Deserialize<PasswordHistoryStorage>(historyJson, _jsonOptions);

                        if (historyData?.PasswordHashes != null && historyData.PasswordHashes.Count > 0 &&
                            historyData.LastUpdatedUtc > mostRecentDate)
                        {
                            mostRecentHash = historyData.PasswordHashes[0];
                            mostRecentDate = historyData.LastUpdatedUtc;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error reading password history blob {BlobName}", blobItem.Name);
                    }
                }
            }

            if (!string.IsNullOrEmpty(mostRecentHash))
            {
                return Result<string>.Success(mostRecentHash);
            }
            else
            {
                return Result<string>.Failure("No password history found for user", ErrorCodes.UserNotFound);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password history retrieval error for user {UserId}", userId);
            return Result<string>.Failure($"Error accessing password history: {ex.Message}", ErrorCodes.StorageError);
        }
    }

    /// <summary>
    /// Sends password changed notification email
    /// </summary>
    private async Task SendPasswordChangedNotification(string userId, CancellationToken cancellationToken)
    {
        try
        {
            // Get user email from Graph API
            var user = await _graphServiceClient.Users[userId]
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Select = new[] { "mail", "userPrincipalName" };
                }, cancellationToken);

            var email = user?.Mail ?? user?.UserPrincipalName;
            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("Could not determine email address for user {UserId} - skipping password changed notification", userId);
                return;
            }

            var correlationId = Guid.NewGuid().ToString();
            _logger.LogInformation("Sending password changed notification to {Email} for user {UserId} [CorrelationId: {CorrelationId}]",
                email, userId, correlationId);

            // Send password changed notification
            var emailSent = await _emailService.SendPasswordChangedNotificationAsync(email, correlationId);

            if (emailSent)
            {
                _logger.LogInformation("Password changed notification sent successfully to {Email} [CorrelationId: {CorrelationId}]", email, correlationId);
            }
            else
            {
                _logger.LogError("Failed to send password changed notification to {Email} [CorrelationId: {CorrelationId}]", email, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password changed notification for user {UserId}: {Message}", userId, ex.Message);
        }
    }
}
