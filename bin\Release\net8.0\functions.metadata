[{"name": "AuthenticationService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.AuthenticationFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Anonymous", "methods": ["post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}, {"name": "PasswordService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.PasswordFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Anonymous", "methods": ["post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}, {"name": "UtilityService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.UtilityFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Anonymous", "methods": ["get", "post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}]