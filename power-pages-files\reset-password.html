<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">

<!-- Secure configuration and user data extraction -->
<script>
  // Secure configuration management - get from Power Pages settings
  window.appConfig = {
    functionUrl: "{{ settings['AzureFunctionUrl'] }}" || null,
    msalClientId: "{{ settings['MSALClientId'] }}" || null,
    msalTenantId: "{{ settings['MSALTenantId'] }}" || null,
    applicationName: "{{ settings['ApplicationName'] }}" || "ApplicationNameNotSet", // Enhanced: Application name
    entraTenantDomain: "{{ settings['EntraTenantDomain'] }}" || "entraexternaltestosler.ciamlogin.com", // Configurable tenant domain
    // Power Pages provider configuration removed - using direct custom authentication
  };

  // Debug logging for configuration
  console.log("Power Pages Configuration Debug:", {
    rawApplicationName: "{{ settings['ApplicationName'] }}",
    rawMSALTenantId: "{{ settings['MSALTenantId'] }}",
    rawMSALClientId: "{{ settings['MSALClientId'] }}",
    rawEntraTenantDomain: "{{ settings['EntraTenantDomain'] }}",
    finalApplicationName: window.appConfig.applicationName,
    entraTenantDomain: window.appConfig.entraTenantDomain,
    msalTenantId: window.appConfig.msalTenantId,
    msalClientId: window.appConfig.msalClientId ? "configured" : "missing",
    allSettings: {
      ApplicationName: "{{ settings['ApplicationName'] }}",
      MSALTenantId: "{{ settings['MSALTenantId'] }}",
      MSALClientId: "{{ settings['MSALClientId'] }}",
      EntraTenantDomain: "{{ settings['EntraTenantDomain'] }}"
    }
  });

  // Extract reset token from URL for Power Pages context
  const resetUrlParams = new URLSearchParams(window.location.search);
  const resetToken = resetUrlParams.get('token');

  // Debug logging for HTML token extraction
  console.log('🔍 HTML Token Extraction:', {
    url: window.location.href,
    search: window.location.search,
    token: resetToken ? 'Found (length: ' + resetToken.length + ')' : 'Not found'
  });

  // For token-based password reset, user won't be logged in
  // Only extract user info if this is a regular password change (no token)
  const userEmail = resetToken ? null : ("{{ user.email }}" || null);
  const userId = resetToken ? null : ("{{ user.id }}" || null);

  // Store in secure global object
  window.liquidUser = {
    applicationName: window.appConfig.applicationName,
    resetToken: resetToken,
    email: userEmail,
    id: userId,
    isTokenBasedReset: !!resetToken
  };

  // Secure user data extraction with input sanitization
  function sanitizeUserInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  }

  // Secure debug logging (only in development)
  if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    console.log("Debug - Reset password page loaded:", {
      hasConfig: !!window.appConfig.functionUrl,
      applicationId: window.appConfig.applicationId,
      hasToken: !!resetToken
    });
  }
</script>

<div class="col-lg-12 columnBlockLayout">
<div class="row sectionBlockLayout text-start">
  <div class="container container-flex">
    <div class="container reset-password-container">
      <h2>Reset Your Password</h2>
      <p>Enter the verification code from your email and your new password.</p>
      <div id="errorMessage" role="alert" class="alert alert-danger hidden"></div>
      <div id="successMessage" role="alert" class="alert alert-success hidden"></div>
      
      <form id="passwordForm">
        <!-- Password reset fields -->
        <div id="passwordFields">

        <!-- Verification Code field -->
        <div class="form-group mb-3" id="verificationCodeGroup">
          <label for="verificationCode" class="form-label fw-bold">Verification Code</label>
          <div class="input-group">
            <input type="text" id="verificationCode" class="form-control" placeholder="Enter the 6-digit code from your email" maxlength="6" pattern="[0-9]{6}" autocomplete="one-time-code">
            <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
          </div>
          <div class="form-text">Enter the 6-digit verification code sent to your email</div>
          <div class="invalid-feedback" id="verificationCodeError"></div>
        </div>

        <!-- New Password field -->
        <div class="form-group mb-3">
          <label for="newPassword" class="form-label fw-bold">New Password</label>
          <div class="input-group">
            <input type="password" id="newPassword" aria-describedby="passwordHelpBlock" required class="form-control" placeholder="Enter your new password" autocomplete="new-password">
            <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword" aria-label="Toggle new password visibility">
              <i class="fas fa-eye"></i>
            </button>
          </div>
          <div id="passwordHelpBlock" class="form-text">Password must be at least 8 characters, include uppercase, lowercase, numbers, special characters, and not be one of your last 12 passwords.</div>
          <div class="invalid-feedback" id="newPasswordError"></div>
        </div>

        <!-- Confirm Password -->
        <div class="form-group mb-3">
          <label for="confirmPassword" class="form-label fw-bold">Confirm New Password</label>
          <div class="input-group">
            <input type="password" id="confirmPassword" required class="form-control" placeholder="Confirm your new password" autocomplete="new-password">
            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword" aria-label="Toggle confirm password visibility">
              <i class="fas fa-eye"></i>
            </button>
          </div>
          <div class="invalid-feedback" id="confirmPasswordError"></div>
        </div>

        <button type="submit" id="submitButton" class="btn btn-primary">Reset Password</button>
        </div> <!-- Close passwordFields div -->
      </form>
      
      <!-- Navigation Links -->
      <div class="text-center mt-3">
        <p><a href="/" class="btn btn-outline-secondary">Back to Home</a></p>
      </div>
    </div>
  </div>
</div>
</div>



<!-- Include the reset password JavaScript file -->
<script src="/reset-password.js"></script>
