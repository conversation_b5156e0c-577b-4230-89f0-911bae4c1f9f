const SecureConfig = {
  getFunctionUrl(functionName = 'AuthenticationService') {
    const baseUrl = window.appConfig?.functionUrl ||
           document.querySelector('meta[name="function-url"]')?.content ||
           document.querySelector('meta[name="azure-function-url"]')?.content ||
           null;

    if (!baseUrl) {
      console.error('Azure Function URL not configured in Power Pages settings');
      return null;
    }

    return `${baseUrl}/api/${functionName}`;
  },

  getMSALConfig() {
    return {
      clientId: window.appConfig?.msalClientId ||
                document.querySelector('meta[name="msal-client-id"]')?.content ||
                null,
      tenantId: window.appConfig?.msalTenantId ||
                document.querySelector('meta[name="msal-tenant-id"]')?.content ||
                null
    };
  }
};

const AZURE_FUNCTION_URL = SecureConfig.getFunctionUrl('AuthenticationService');
const msalConfigData = SecureConfig.getMSALConfig();
const MSAL_CLIENT_ID = msalConfigData.clientId;
const MSAL_TENANT_ID = msalConfigData.tenantId;
const MSAL_REDIRECT_URI = window.location.origin;

// Use APPLICATION_NAME as the primary identifier (consistent with backend)
const APPLICATION_NAME = window.appConfig?.applicationName ||
                        document.querySelector('meta[name="application-name"]')?.content ||
                        "ApplicationNameNotSet";

if (!AZURE_FUNCTION_URL) {
  console.error("Azure Function URL not configured");
}

if (!APPLICATION_NAME || APPLICATION_NAME === "ApplicationNameNotSet") {
  console.warn("Application Name not configured - check Power Pages settings");
}

// Validate Entra External ID configuration for authentication redirects
const entraTenantDomain = window.appConfig?.entraTenantDomain;
const tenantId = window.appConfig?.msalTenantId;

if (!entraTenantDomain || entraTenantDomain.includes('ERROR_MISSING')) {
  console.warn("Entra Tenant Domain not configured - authentication redirects may fail");
}

if (!tenantId || tenantId.includes('ERROR_MISSING')) {
  console.warn("MSAL Tenant ID not configured - authentication redirects may fail");
}

const InputSanitizer = {
  sanitizeString(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validatePassword(password) {
    if (!password || typeof password !== 'string') return false;
    if (password.length < 8 || password.length > 128) return false;
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]).{8,}$/.test(password);
  },

  validateEmail(email) {
    if (!email || typeof email !== 'string') return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  },

  validateName(name) {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();
    return trimmed.length >= 1 && trimmed.length <= 50 && /^[a-zA-Z\s'-]+$/.test(trimmed);
  }
};

class ClientRateLimit {
  constructor(maxRequests = 3, windowMs = 60000) {
    this.requests = [];
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  canMakeRequest() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);

    if (this.requests.length >= this.maxRequests) {
      return false;
    }

    this.requests.push(now);
    return true;
  }

  getTimeUntilNextRequest() {
    if (this.requests.length === 0) return 0;
    const oldestRequest = Math.min(...this.requests);
    const timeUntilReset = this.windowMs - (Date.now() - oldestRequest);
    return Math.max(0, timeUntilReset);
  }
}

const rateLimiter = new ClientRateLimit(3, 60000);

const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const registerButton = $('#registerButton');
const registrationForm = $('#registrationForm');
const registrationFieldsDiv = $('#registrationFields');
const toggleButtons = {
  password: $('#togglePassword'),
  confirm: $('#toggleConfirmPassword')
};

const msalConfig = {
  auth: {
    clientId: MSAL_CLIENT_ID,
    authority: `https://login.microsoftonline.com/${MSAL_TENANT_ID}`,
    redirectUri: MSAL_REDIRECT_URI
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false
  }
};

const msalInstance = new msal.PublicClientApplication(msalConfig);

msalInstance.handleRedirectPromise()
  .then(tokenResponse => {
    if (tokenResponse) {
      msalInstance.setActiveAccount(tokenResponse.account);
    }
  })
  .catch(error => {
    console.error("MSAL error:", error);
    showMessage("Authentication error");
  });

function showMessage(message, isError = true, timeout = 0) {
  errorMessageDiv.addClass('d-none');
  successMessageDiv.addClass('d-none');
  const messageDiv = isError ? errorMessageDiv : successMessageDiv;
  messageDiv.html(message).removeClass('d-none');
  if (timeout > 0) {
    setTimeout(() => messageDiv.addClass('d-none'), timeout);
  }
}

function showLoadingState(message) {
  registerButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  registerButton.prop('disabled', false).text('Create Account');
}

function validatePasswordComplexity(password) {
  if (!password || password.length < 8) return false;
  return /[A-Z]/.test(password) &&
         /[a-z]/.test(password) &&
         /\d/.test(password) &&
         /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(password);
}

function togglePasswordVisibility(inputId, toggleButton) {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');
  
  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
}

// Core Registration Function
async function registerUser(userData) {
  // Rate limiting check
  if (!rateLimiter.canMakeRequest()) {
    const waitTime = Math.ceil(rateLimiter.getTimeUntilNextRequest() / 1000);
    throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
  }

  showLoadingState('Creating Account...');
  try {
    // Input sanitization and validation
    const sanitizedData = {
      email: InputSanitizer.sanitizeString(userData.email),
      password: InputSanitizer.sanitizeString(userData.password),
      firstName: InputSanitizer.sanitizeString(userData.firstName),
      lastName: InputSanitizer.sanitizeString(userData.lastName)
    };

    if (!InputSanitizer.validateEmail(sanitizedData.email)) {
      throw new Error('Invalid email format');
    }

    if (!InputSanitizer.validatePassword(sanitizedData.password)) {
      throw new Error('Password does not meet security requirements');
    }

    if (!InputSanitizer.validateName(sanitizedData.firstName)) {
      throw new Error('Invalid first name');
    }

    if (!InputSanitizer.validateName(sanitizedData.lastName)) {
      throw new Error('Invalid last name');
    }

    const response = await fetch(`${AZURE_FUNCTION_URL}?operation=register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        email: sanitizedData.email,
        password: sanitizedData.password,
        firstName: sanitizedData.firstName,
        lastName: sanitizedData.lastName,
        applicationName: APPLICATION_NAME,
        registrationSource: 'PowerPages'
      })
    });

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const textResponse = await response.text();
      throw new Error(`Non-JSON response: ${response.status}. Response: ${textResponse.substring(0, 200)}`);
    }

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || `Registration error ${response.status}`);
    }

    if (result.success === false) {
      throw new Error(result.message || "Registration failed. Please try again.");
    }

    return { success: true, message: result.message || "Account created successfully!" };
  } catch (error) {
    console.error("Registration error:", error);
    throw error;
  } finally {
    resetLoadingState();
  }
}

// Client-side form validation
function validateRegistrationForm() {
  let isValid = true;
  
  // Clear previous validation states
  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  // Email validation
  const email = $('#email').val();
  if (!InputSanitizer.validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  // First name validation
  const firstName = $('#firstName').val();
  if (!InputSanitizer.validateName(firstName)) {
    $('#firstNameError').text('Please enter a valid first name (letters, spaces, hyphens, and apostrophes only)');
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  // Last name validation
  const lastName = $('#lastName').val();
  if (!InputSanitizer.validateName(lastName)) {
    $('#lastNameError').text('Please enter a valid last name (letters, spaces, hyphens, and apostrophes only)');
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  // Password validation
  const password = $('#password').val();
  if (!InputSanitizer.validatePassword(password)) {
    $('#passwordError').text('Password must meet complexity requirements');
    $('#password').addClass('is-invalid');
    isValid = false;
  }

  // Confirm password validation
  const confirmPassword = $('#confirmPassword').val();
  if (password !== confirmPassword) {
    $('#confirmPasswordError').text('Passwords do not match');
    $('#confirmPassword').addClass('is-invalid');
    isValid = false;
  }

  // Terms acceptance validation
  const termsAccepted = $('#termsAccepted').is(':checked');
  if (!termsAccepted) {
    $('#termsError').text('You must accept the terms and conditions');
    $('#termsAccepted').addClass('is-invalid');
    isValid = false;
  }

  return isValid;
}

// Event Handlers
function initializeFormHandlers() {
  // Password visibility toggles
  if (toggleButtons.password && toggleButtons.password.length) {
    toggleButtons.password.click(() => togglePasswordVisibility('password', toggleButtons.password));
  }
  if (toggleButtons.confirm && toggleButtons.confirm.length) {
    toggleButtons.confirm.click(() => togglePasswordVisibility('confirmPassword', toggleButtons.confirm));
  }

  // Form submit handler
  registrationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false); // Clear previous messages

    try {
      // Client-side validation
      if (!validateRegistrationForm()) {
        return;
      }

      // Collect form data
      const userData = {
        email: $('#email').val(),
        password: $('#password').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val()
      };

      // Register user
      const result = await registerUser(userData);
      
      if (result.success) {
        showMessage("Account created successfully! Redirecting to home page...", false);
        registrationForm[0].reset();

        // Redirect to home page where Power Pages will handle authentication
        // This approach lets Power Pages manage the authentication flow naturally
        setTimeout(() => {
          const email = userData.email;

          console.log(`Registration successful for: ${email}`);
          console.log(`Redirecting to home page - Power Pages will handle authentication`);

          // Store the email for potential use by Power Pages
          sessionStorage.setItem('registeredEmail', email);
          sessionStorage.setItem('justRegistered', 'true');

          // Redirect to home page - Power Pages will automatically redirect to authentication if needed
          window.location.href = '/';
        }, 2000); // 2 second delay to ensure user creation is complete
      }

    } catch (error) {
      console.error("Form submission error:", error);
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    }
  });
}

// Initialize the application when the document is ready
$(document).ready(function() {
  initializeFormHandlers();
});
