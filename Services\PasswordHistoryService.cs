using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace PasswordHistoryValidator.Services;

public class PasswordHistoryService : IPasswordHistoryService
{
    private readonly ILogger<PasswordHistoryService> _logger;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string ContainerName = "passwordhistory";
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

    public PasswordHistoryService(
        ILogger<PasswordHistoryService> logger,
        BlobServiceClient blobServiceClient,
        IMemoryCache cache,
        IConfiguration configuration,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _blobServiceClient = blobServiceClient ?? throw new ArgumentNullException(nameof(blobServiceClient));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _jsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
    }

    public async Task<Result<List<string>>> GetPasswordHistoryAsync(string userId, CancellationToken cancellationToken = default)
    {
        var correlationId = Guid.NewGuid().ToString();

        try
        {
            var cacheKey = $"password_history_{userId}";
            if (_cache.TryGetValue(cacheKey, out List<string>? cachedHistory))
            {
                return Result<List<string>>.Success(cachedHistory ?? new List<string>());
            }

            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
            await containerClient.CreateIfNotExistsAsync(PublicAccessType.None, cancellationToken: cancellationToken);

            string blobName = $"{userId}.json";
            var blobClient = containerClient.GetBlobClient(blobName);

            if (!await blobClient.ExistsAsync(cancellationToken: cancellationToken))
            {
                var emptyHistory = new List<string>();
                _cache.Set(cacheKey, emptyHistory, TimeSpan.FromMinutes(5));
                return Result<List<string>>.Success(emptyHistory);
            }

            var downloadResult = await blobClient.DownloadContentAsync(cancellationToken: cancellationToken);
            var historyJson = downloadResult.Value.Content.ToString();

            var historyData = JsonSerializer.Deserialize<PasswordHistoryStorage>(historyJson, _jsonOptions);
            var passwordHashes = historyData?.PasswordHashes ?? new List<string>();

            _cache.Set(cacheKey, passwordHashes, _cacheExpiration);
            return Result<List<string>>.Success(passwordHashes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving password history for user {UserId} [CorrelationId: {CorrelationId}]",
                userId, correlationId);
            return Result<List<string>>.Failure(
                "Error accessing password history",
                ErrorCodes.StorageError,
                correlationId);
        }
    }

    public async Task<Result<bool>> UpdatePasswordHistoryAsync(string userId, string newPassword, CancellationToken cancellationToken = default)
    {
        var correlationId = Guid.NewGuid().ToString();

        try
        {
            var historyResult = await GetPasswordHistoryAsync(userId, cancellationToken);
            if (!historyResult.IsSuccess)
            {
                return Result<bool>.Failure(historyResult.ErrorMessage, historyResult.ErrorCode, correlationId);
            }

            var passwordHashes = historyResult.Value ?? new List<string>();
            var maxHistoryCount = _configuration.GetValue<int>("PasswordHistory:MaxCount", 12);
            var workFactor = _configuration.GetValue<int>("PasswordHistory:WorkFactor", 12);

            string newPasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword, workFactor);

            passwordHashes.Insert(0, newPasswordHash);
            if (passwordHashes.Count > maxHistoryCount)
            {
                passwordHashes = passwordHashes.Take(maxHistoryCount).ToList();
            }

            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
            await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);

            string blobName = $"{userId}.json";
            var blobClient = containerClient.GetBlobClient(blobName);

            var newHistoryData = new PasswordHistoryStorage
            {
                UserId = userId,
                LastUpdatedUtc = DateTime.UtcNow,
                PasswordHashes = passwordHashes
            };

            string jsonHistory = JsonSerializer.Serialize(newHistoryData, _jsonOptions);
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonHistory));

            var metadata = new Dictionary<string, string>
            {
                { "lastUpdated", DateTime.UtcNow.ToString("o") },
                { "recordCount", passwordHashes.Count.ToString() },
                { "correlationId", correlationId }
            };

            var options = new BlobUploadOptions
            {
                Metadata = metadata,
                HttpHeaders = new BlobHttpHeaders { ContentType = "application/json" }
            };

            await blobClient.UploadAsync(stream, options, cancellationToken);

            var cacheKey = $"password_history_{userId}";
            _cache.Set(cacheKey, passwordHashes, _cacheExpiration);

            _logger.LogInformation("Updated password history for user {UserId} with {Count} total entries [CorrelationId: {CorrelationId}]",
                userId, passwordHashes.Count, correlationId);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating password history for user {UserId} [CorrelationId: {CorrelationId}]",
                userId, correlationId);
            return Result<bool>.Failure(
                "Error updating password history",
                ErrorCodes.StorageError,
                correlationId);
        }
    }

    public async Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string userId, string password, CancellationToken cancellationToken = default)
    {
        var correlationId = Guid.NewGuid().ToString();

        try
        {
            var historyResult = await GetPasswordHistoryAsync(userId, cancellationToken);
            if (!historyResult.IsSuccess)
            {
                return Result<bool>.Failure(historyResult.ErrorMessage, historyResult.ErrorCode, correlationId);
            }

            var passwordHashes = historyResult.Value ?? new List<string>();

            foreach (var hash in passwordHashes)
            {
                if (!string.IsNullOrEmpty(hash) && BCrypt.Net.BCrypt.Verify(password, hash))
                {
                    _logger.LogWarning("Password reuse detected for user {UserId} [CorrelationId: {CorrelationId}]",
                        userId, correlationId);
                    return Result<bool>.Failure(
                        "Password has been used recently. Please choose a different password.",
                        ErrorCodes.PasswordInHistory,
                        correlationId);
                }
            }

            _logger.LogDebug("Password validation successful for user {UserId} [CorrelationId: {CorrelationId}]",
                userId, correlationId);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating password for user {UserId} [CorrelationId: {CorrelationId}]",
                userId, correlationId);
            return Result<bool>.Failure(
                "Error validating password",
                ErrorCodes.ValidationError,
                correlationId);
        }
    }

    // Application-scoped methods
    public async Task<Result<List<string>>> GetPasswordHistoryAsync(string applicationId, string userId, CancellationToken cancellationToken = default)
    {
        var applicationScopedUserId = GetApplicationScopedUserId(applicationId, userId);
        return await GetPasswordHistoryAsync(applicationScopedUserId, cancellationToken);
    }

    public async Task<Result<bool>> UpdatePasswordHistoryAsync(string applicationId, string userId, string newPassword, CancellationToken cancellationToken = default)
    {
        var applicationScopedUserId = GetApplicationScopedUserId(applicationId, userId);
        return await UpdatePasswordHistoryAsync(applicationScopedUserId, newPassword, cancellationToken);
    }

    public async Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string applicationId, string userId, string password, CancellationToken cancellationToken = default)
    {
        var applicationScopedUserId = GetApplicationScopedUserId(applicationId, userId);
        return await ValidatePasswordAgainstHistoryAsync(applicationScopedUserId, password, cancellationToken);
    }

    // Create application-scoped user identifier for storage isolation
    private string GetApplicationScopedUserId(string applicationId, string userId)
    {
        // Sanitize applicationId to prevent path traversal
        var sanitizedAppId = applicationId
            .Replace("/", "")
            .Replace("\\", "")
            .Replace("..", "")
            .Replace(":", "")
            .Trim();

        if (string.IsNullOrEmpty(sanitizedAppId))
        {
            throw new ArgumentException("Application ID cannot be empty or contain only invalid characters", nameof(applicationId));
        }


        return $"{sanitizedAppId}/{userId}";
    }
}

public class PasswordHistoryStorage
{
    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("lastUpdatedUtc")]
    public DateTime LastUpdatedUtc { get; set; }

    [JsonPropertyName("passwordHashes")]
    public List<string> PasswordHashes { get; set; } = new();
}
